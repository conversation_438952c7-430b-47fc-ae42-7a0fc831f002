# AbstractSplittableReport 优化指南

## 概述

本文档描述了 `AbstractSplittableReport` 类的优化方案，旨在解决合并报告和拆分报告之间的代码重复问题，提高代码的可维护性。

## 问题分析

### 原有问题
1. **代码重复严重**：`process()` 和 `processForBusinessSystem()` 方法包含几乎相同的逻辑
2. **维护成本高**：任何业务逻辑变更需要在两个方法中同步修改
3. **过滤逻辑缺失**：拆分模式下没有真正按业务系统过滤数据
4. **扩展性差**：添加新的过滤维度需要大量代码修改

### 影响范围
- JiangSuReport：约140行重复代码
- TdSpecReport：文件复制方式处理拆分
- SpecReport：文件复制方式处理拆分

## 优化方案

### 核心设计思想
1. **统一处理入口**：使用单一的 `processInternal()` 方法处理所有场景
2. **上下文驱动**：通过 `BusinessSystemContext` 控制过滤行为
3. **钩子方法**：在数据获取点提供过滤机制
4. **向后兼容**：保持现有API不变

### 关键组件

#### 1. BusinessSystemContext 类
```java
public static class BusinessSystemContext {
    private final TreeLabelDTO businessSystem;
    private final boolean isFiltered;
    
    // 创建非过滤上下文（合并模式）
    public static BusinessSystemContext unfiltered();
    
    // 创建过滤上下文（拆分模式）
    public static BusinessSystemContext filtered(TreeLabelDTO businessSystem);
}
```

#### 2. 统一处理方法
```java
protected abstract String processInternal(ExportWordDto dto, 
                                        QueryProjectOperationExportVo poVo, 
                                        Long modelId, 
                                        BusinessSystemContext context) throws Exception;
```

#### 3. 过滤钩子方法
```java
// 根据业务系统名称过滤
protected <T> List<T> filterDataByContext(List<T> dataList, 
                                         BusinessSystemContext context,
                                         Function<T, String> systemFieldExtractor);

// 根据业务系统ID过滤
protected <T> List<T> filterDataByContextId(List<T> dataList, 
                                           BusinessSystemContext context,
                                           Function<T, String> systemIdExtractor);
```

## 迁移指南

### 步骤1：重写 processInternal 方法
将原有的 `process()` 方法逻辑迁移到 `processInternal()` 方法中：

```java
@Override
protected String processInternal(ExportWordDto dto, QueryProjectOperationExportVo poVo, 
                               Long modelId, BusinessSystemContext context) throws Exception {
    // 原有的process()方法逻辑
    // ...
    
    // 关键改动：在数据获取点应用过滤
    List<CoInventory> coInventoryList = getFilteredInventoryData(operationId, context);
    
    // 关键改动：根据上下文生成文件名
    String originalFileName = String.format("%s报告.docx", poVo.getOperationName());
    String realFileName = generateFileName(originalFileName, context);
    
    // ...
}
```

### 步骤2：添加数据过滤钩子
在每个数据获取点添加过滤逻辑：

```java
private List<CoInventory> getFilteredInventoryData(String operationId, BusinessSystemContext context) {
    // 获取原始数据
    List<CoInventory> coInventoryList = coInventoryMapper.selectList(queryWrapper);
    
    // 应用过滤
    return filterDataByContext(coInventoryList, context, CoInventory::getBusSystem);
}
```

### 步骤3：删除重复方法
删除原有的 `processForBusinessSystem()` 方法实现，因为基类已提供默认实现。

### 步骤4：测试验证
1. 测试合并模式：确保原有功能正常
2. 测试拆分模式：验证数据正确过滤
3. 测试文件名生成：确保包含业务系统名称

## 使用示例

### 基本用法
```java
@Component
public class MyReport extends AbstractSplittableReport {
    
    @Override
    protected String processInternal(ExportWordDto dto, QueryProjectOperationExportVo poVo, 
                                   Long modelId, BusinessSystemContext context) throws Exception {
        
        // 1. 获取并过滤数据
        List<MyData> dataList = getFilteredData(dto.getOperationId(), context);
        
        // 2. 构建模型
        Map<String, Object> model = buildModel(dataList);
        
        // 3. 生成文件名
        String fileName = generateFileName("report.docx", context);
        
        // 4. 渲染和输出
        return renderAndSave(model, fileName);
    }
    
    private List<MyData> getFilteredData(String operationId, BusinessSystemContext context) {
        List<MyData> rawData = dataMapper.selectByOperationId(operationId);
        return filterDataByContext(rawData, context, MyData::getBusinessSystem);
    }
}
```

### 高级过滤
```java
// 多字段过滤
private List<ComplexData> getFilteredComplexData(String operationId, BusinessSystemContext context) {
    List<ComplexData> rawData = complexDataMapper.selectByOperationId(operationId);
    
    if (!context.isFiltered()) {
        return rawData;
    }
    
    String targetSystem = context.getBusinessSystemName();
    return rawData.stream()
        .filter(data -> targetSystem.equals(data.getPrimarySystem()) || 
                       targetSystem.equals(data.getSecondarySystem()))
        .collect(Collectors.toList());
}
```

## 性能考虑

### 优化点
1. **减少重复查询**：数据只查询一次，然后根据需要过滤
2. **延迟过滤**：只在需要时进行过滤操作
3. **内存优化**：避免创建不必要的数据副本

### 注意事项
1. **大数据集**：对于大数据集，考虑在数据库层面进行过滤
2. **复杂过滤**：复杂的过滤逻辑可能影响性能，需要权衡
3. **缓存策略**：可以考虑缓存过滤结果

## 向后兼容性

### 保持兼容的API
- `process(dto, poVo, modelId)` - 委托给 `processInternal()`
- `processForBusinessSystem()` - 标记为 `@Deprecated`，委托给 `processInternal()`
- `processWithSplit()` - 保持不变，内部使用优化逻辑

### 迁移建议
1. **渐进式迁移**：可以逐个报告类进行迁移
2. **保持测试**：确保迁移后功能不变
3. **文档更新**：更新相关文档和注释

## 总结

通过这次优化，我们实现了：
1. **消除代码重复**：从~140行重复代码减少到0
2. **提高可维护性**：单一修改点，统一逻辑
3. **增强功能**：真正的业务系统数据过滤
4. **保持兼容性**：现有代码无需修改即可工作
5. **提升扩展性**：易于添加新的过滤维度

这个优化方案为报告系统的长期维护和扩展奠定了良好的基础。
