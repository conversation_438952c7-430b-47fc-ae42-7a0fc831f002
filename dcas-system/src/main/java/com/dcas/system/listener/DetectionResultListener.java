package com.dcas.system.listener;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.fastjson2.JSONObject;
import com.dcas.common.enums.DataSourceType;
import com.dcas.common.exception.ServiceException;
import com.dcas.common.domain.entity.DetectionResult;
import com.dcas.common.model.dto.SecurityRuleDTO;
import com.dcas.common.model.excel.RiskDetectionExcel;
import com.dcas.system.service.impl.DetectionResultServiceImpl;
import com.google.common.collect.Lists;
import org.springframework.cglib.beans.BeanCopier;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2023/10/13 11:23
 * @since 1.5.0
 */
public class DetectionResultListener implements ReadListener<RiskDetectionExcel> {

    private static final String TRUE = "符合";

    private final static int BATCH_COUNT = 1000;
    private final List<DetectionResult> dataList = Lists.newArrayListWithCapacity(BATCH_COUNT);

    private final BeanCopier copier = BeanCopier.create(RiskDetectionExcel.class, DetectionResult.class, false);

    private final String operationId;
    private final DetectionResultServiceImpl service;
    private final Map<Integer, Map<String, SecurityRuleDTO>> ruleGroup;

    public DetectionResultListener(DetectionResultServiceImpl service, String operationId, Map<Integer, Map<String, SecurityRuleDTO>> ruleGroup) {
        this.operationId = operationId;
        this.service = service;
        this.ruleGroup = ruleGroup;
    }

    @Override
    public void invoke(RiskDetectionExcel data, AnalysisContext context) {
        if (CharSequenceUtil.isEmpty(data.getResult())  && CharSequenceUtil.isEmpty(data.getAccord())){
            throw new ServiceException("检测结果不正确，请重新生成后再次导入");
        }
        DetectionResult bean = new DetectionResult();
        copier.copy(data, bean, null);
        bean.setJobId(-1L);
        bean.setOption(data.getOption());
        bean.setContent(data.getContent());
        bean.setOperationId(operationId);
        bean.setResult(data.getResult());
        bean.setAccord(Objects.nonNull(data.getAccord()) ? Objects.equals(data.getAccord(), TRUE) : null);
        HashMap<String, String> map = new HashMap<>();
        map.put("configName", data.getConfigName());
        map.put("host", data.getIp());
        map.put("port", data.getPort());
        String jsonStr = new JSONObject(map).toString();
        bean.setDbConfig(jsonStr);
        try {
            bean.setDbType(DataSourceType.valueOf(data.getDbType()).getCode().intValue());
        } catch (IllegalArgumentException e) {
            throw new ServiceException(String.format("数据库类型错误：%s", data.getDbType()));
        }
        Map<String, SecurityRuleDTO> ruleMap = ruleGroup.get(bean.getDbType());
        if (Objects.nonNull(ruleMap)) {
            SecurityRuleDTO dto = ruleMap.get(bean.getOption() + bean.getContent());
            if (Objects.nonNull(dto)) {
                bean.setRuleId(dto.getRuleId());
                bean.setUnqualified(dto.getUnqualified());
            }
        }
        dataList.add(bean);
        if (dataList.size() >= BATCH_COUNT) {
            service.saveBatch(dataList);
            dataList.clear();
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        service.saveBatch(dataList);
    }

}
