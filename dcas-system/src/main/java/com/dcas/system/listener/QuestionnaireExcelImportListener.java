package com.dcas.system.listener;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.dcas.common.exception.ServiceException;
import com.dcas.common.model.dto.OfflineQuestionnaireExcelDTO;
import com.dcas.common.model.dto.QuestionnaireExcelImportDTO;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 调研问卷Excel导入监听器
 *
 * <AUTHOR>
 * @date 2024/12/19
 * @since 1.0.0
 */
@Slf4j
public class QuestionnaireExcelImportListener implements ReadListener<QuestionnaireExcelImportDTO> {

    private final List<OfflineQuestionnaireExcelDTO.QuestionnaireRowData> rowDataList = new ArrayList<>();
    private final String sheetName;

    public QuestionnaireExcelImportListener(String sheetName) {
        this.sheetName = sheetName;
    }

    @Override
    public void invoke(QuestionnaireExcelImportDTO data, AnalysisContext context) {
        // 跳过空行或核查项为空的行
        if (data == null || StrUtil.isEmpty(data.getItem())) {
            return;
        }

        // 校验调研结果
        String result = StrUtil.isNotEmpty(data.getResult()) ? data.getResult().trim() : "";
        if (StrUtil.isEmpty(result)) {
            throw new ServiceException("文件导入失败，调研结果未完成，请填写完成后再次导入。");
        }

        if (!Arrays.asList("符合", "不符合", "不适用").contains(result)) {
            int currentRowIndex = context.readRowHolder().getRowIndex() + 1;
            throw new ServiceException(String.format("文件导入失败，第%d行调研结果填写不正确，只能填写：符合、不符合、不适用", currentRowIndex));
        }

        if (StrUtil.isNotBlank(data.getDescription()) && data.getDescription().trim().length() > 200) {
            int currentRowIndex = context.readRowHolder().getRowIndex() + 1;
            throw new ServiceException(String.format("文件导入失败，第%d行现状描述超过最大字符限制", currentRowIndex));
        }

        // 转换为统一的数据格式
        OfflineQuestionnaireExcelDTO.QuestionnaireRowData rowData = OfflineQuestionnaireExcelDTO.QuestionnaireRowData.builder()
                .question(StrUtil.isNotEmpty(data.getQuestion()) ? data.getQuestion().trim() : "")
                .item(data.getItem().trim())
                .result(result)
                .description(StrUtil.isNotEmpty(data.getDescription()) ? data.getDescription().trim() : "")
                .rowIndex(context.readRowHolder().getRowIndex() + 1)
                .build();

        rowDataList.add(rowData);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        log.info("Sheet页 {} 解析完成，共解析 {} 条数据", sheetName, rowDataList.size());
    }

    public List<OfflineQuestionnaireExcelDTO.QuestionnaireRowData> getRowDataList() {
        return rowDataList;
    }
}
