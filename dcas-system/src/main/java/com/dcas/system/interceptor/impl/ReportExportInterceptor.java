package com.dcas.system.interceptor.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dcas.common.domain.entity.CoLicense;
import com.dcas.common.enums.AttributeEnum;
import com.dcas.common.enums.CommonResultCode;
import com.dcas.common.exception.ServiceException;
import com.dcas.common.mapper.CoLicenseMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @className ReportExportInterceptor
 * @description 报告导出拦截器
 * @date 2025/06/18 11:34
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ReportExportInterceptor implements HandlerInterceptor {

    private final CoLicenseMapper coLicenseMapper;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
        throws Exception {
        checkLicenseType();
        return true;
    }

    /**
     * 检查license类型，若IncType=ONTRAIL试用，则限制报告无法导出
     */
    public void checkLicenseType() {
        CoLicense coLicense = coLicenseMapper.selectOne(
            new QueryWrapper<CoLicense>().eq("attribute", AttributeEnum.CLIENT_GRANT.getAttribute()));
        // 测试版本，无法导出报告
        if (coLicense != null && "ONTRAIL".equals(coLicense.getLncType())) {
            throw new ServiceException(CommonResultCode.MC_BASE_EDITION_WARN);
        }
    }
}
