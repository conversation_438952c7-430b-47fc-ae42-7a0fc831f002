package com.dcas.system.interceptor.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dcas.common.enums.AttributeEnum;
import com.dcas.common.exception.ServiceException;
import com.dcas.common.domain.entity.CoLicense;
import com.dcas.common.mapper.CoLicenseMapper;
import com.dcas.system.service.CoLicenseService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Objects;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2023/1/10 9:43
 * @since 1.0.1
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class LicenseInterceptor implements HandlerInterceptor {

    private final CoLicenseMapper coLicenseMapper;
    private final CoLicenseService coLicenseService;

    /**
     * 系统升级接口uri
     */
    private final static String UPGRADE_URI = "/api/upgrade/start";

    /**
     * 知识库升级接口uri
     */
    private final static String LIBRARY_URI = "/api/upgrade/sync";

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String machineCode = coLicenseService.queryMachineCode();
        QueryWrapper<CoLicense> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("device_id", machineCode);
        String uri = request.getRequestURI();
        String msg = "您的客户端证书已过期，请重新申请！";
        String validateMsg = "您的证书无效，请检查是否取得授权！";
        if (UPGRADE_URI.equals(uri)){
            queryWrapper.eq("attribute", AttributeEnum.UPGRADE_GRANT.getAttribute());
            msg = "您的版本升级授权证书已过期，请重新申请！";
            validateMsg = "您的版本升级授权证书无效，请检查是否取得授权！";
        } else if (LIBRARY_URI.equals(uri)){
            queryWrapper.eq("attribute", AttributeEnum.LIBRARY_GRANT.getAttribute());
            msg = "您的知识库授权证书已过期，请重新申请！";
            validateMsg = "您的知识库授权证书无效，请检查是否取得授权！";
        } else {
            queryWrapper.eq("attribute", AttributeEnum.CLIENT_GRANT.getAttribute());
        }
        CoLicense coLicense = coLicenseMapper.selectOne(queryWrapper);
        if (Objects.isNull(coLicense)){
            throw new ServiceException(validateMsg);
        }
        if (coLicense.getLocked()) {
            throw new ServiceException("系统已被锁定！");
        }
        if (DateUtil.currentSeconds() > coLicense.getAuthorTimeEnd()) {
            throw new ServiceException(msg);
        }
        return true;
    }
}
