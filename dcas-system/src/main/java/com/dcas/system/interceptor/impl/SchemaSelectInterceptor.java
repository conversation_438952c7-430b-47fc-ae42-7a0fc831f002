package com.dcas.system.interceptor.impl;

import com.alibaba.druid.DbType;
import com.alibaba.druid.sql.SQLUtils;
import com.alibaba.druid.sql.ast.SQLStatement;
import com.alibaba.druid.sql.dialect.postgresql.parser.PGSQLStatementParser;
import com.baomidou.mybatisplus.core.toolkit.PluginUtils;
import com.baomidou.mybatisplus.extension.plugins.inner.InnerInterceptor;
import com.dcas.common.visitor.SchemaSwitchVisitorAdapter;
import com.dcas.system.holder.DynamicSchemaContextHolder;
import com.dcas.system.manager.DynamicSchemaManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.executor.statement.StatementHandler;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlCommandType;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;

import java.sql.Connection;

/**
 * <p>
 *     动态选择知识库版本库
 * </p>
 *
 * <AUTHOR>
 * @date 2023/11/14 9:55
 * @since 1.6.0
 */
@Slf4j
public class SchemaSelectInterceptor implements InnerInterceptor {

    @Override
    public void beforeQuery(Executor executor, MappedStatement ms, Object parameter, RowBounds rowBounds, ResultHandler resultHandler, BoundSql boundSql) {
        String sql = boundSql.getSql();
        // 简单判断sql中是否包含涉及的知识库表，不包含的话就不用解析了
        if (notContainsSyncTable(sql)) {
            return;
        }
        String packageSql = packageSql(sql);
        PluginUtils.mpBoundSql(boundSql).sql(packageSql);
    }

    @Override
    public void beforePrepare(StatementHandler sh, Connection connection, Integer transactionTimeout) {
        PluginUtils.MPStatementHandler mpSh = PluginUtils.mpStatementHandler(sh);
        MappedStatement ms = mpSh.mappedStatement();
        SqlCommandType sct = ms.getSqlCommandType();
        PluginUtils.MPBoundSql mpBs = mpSh.mPBoundSql();
        String sql = mpBs.sql();
        if (notContainsSyncTable(sql)) {
            return;
        }

        if (sct == SqlCommandType.INSERT || sct == SqlCommandType.UPDATE || sct == SqlCommandType.DELETE) {
            String packageSql = packageSql(sql);
            mpBs.sql(packageSql);
        }
    }

    private String packageSql(String sql) {
        String version = DynamicSchemaContextHolder.peek();
        String schema = DynamicSchemaManager.getSchema(version);
        PGSQLStatementParser parser = new PGSQLStatementParser(sql);
        SQLStatement sqlStatement = parser.parseStatement();
        // 访问器
        SchemaSwitchVisitorAdapter visitor = new SchemaSwitchVisitorAdapter(schema, DynamicSchemaManager.getSyncTableNames());
        sqlStatement.accept(visitor);
        return SQLUtils.toSQLString(sqlStatement, DbType.postgresql);
    }

    private boolean notContainsSyncTable(String sql) {
        return !DynamicSchemaManager.isContainsSyncTable(sql.toLowerCase());
    }
}
