package com.dcas.system.interceptor.impl;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import com.dcas.common.enums.CommonResultCode;
import com.dcas.common.enums.SysConfigEnum;
import com.dcas.common.enums.SysEditionEnum;
import com.dcas.common.exception.ServiceException;
import com.dcas.system.service.ISysConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @className BaseEditionInterceptor
 * @description 社区版接口限制拦截器
 * @date 2024/04/22 18:49
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class BaseEditionInterceptor implements HandlerInterceptor {

    private final ISysConfigService sysConfigService;

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex)
        throws Exception {
        HandlerInterceptor.super.afterCompletion(request, response, handler, ex);
    }

    private void checkEdition() {
        String edition = sysConfigService.selectConfigByKey(SysConfigEnum.SYSTEM_EDITION.getKey());
    }
}
