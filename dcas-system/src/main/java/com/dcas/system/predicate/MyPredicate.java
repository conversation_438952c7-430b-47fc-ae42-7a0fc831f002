package com.dcas.system.predicate;

import cn.hutool.core.util.StrUtil;
import com.dcas.common.domain.entity.CoVerification;
import com.dcas.common.model.vo.AnalysisStandardItemVO;
import com.dcas.common.model.vo.AnalysisStandardVO;
import lombok.extern.slf4j.Slf4j;

import java.util.function.Predicate;

/**
 * <AUTHOR>
 * @date 2024/04/11
 */
@Slf4j
public class MyPredicate implements Predicate<CoVerification> {

    private final String column;

    public MyPredicate(String column){
        this.column = column;
    }
    @Override
    public boolean test(CoVerification item) {
        switch (column){
            case "stage":
                return StrUtil.isNotEmpty(item.getStage());
            case "process":
                return StrUtil.isNotEmpty(item.getProcess());
            case "dimension":
                return StrUtil.isNotEmpty(item.getDimension());
            case "classify":
                return StrUtil.isNotEmpty(item.getClassify());
            case "level":
                return StrUtil.isNotEmpty(item.getLevel());
            case "bp_code":
                return StrUtil.isNotEmpty(item.getBpCode());
            case "tags":
                return StrUtil.isNotEmpty(item.getType());
            case "content":
                return StrUtil.isNotEmpty(item.getStandardProvision());
            default:
                log.warn("column {} not supported!", column);
                return false;
        }
    }
}
