package com.dcas.system.producer;

import com.dcas.common.domain.entity.Item;
import com.dcas.common.domain.entity.Question;
import com.dcas.common.domain.entity.Template;
import com.dcas.common.domain.entity.TemplateContent;

import java.util.List;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2023/2/14 10:56
 * @since 1.2.0
 */
public interface TemplateProducer {

    /**
     * 生成调研模板
     *
     * @param template 模板配置信息
     * @return 模板内容
     */
    List<TemplateContent> produce(Template template, List<Question> questions, List<Item> items);
}
