package com.dcas.system.producer;

import com.dcas.common.enums.TemplateAlgorithm;
import com.dcas.common.domain.entity.Item;
import com.dcas.common.domain.entity.Question;
import com.dcas.common.domain.entity.Template;
import com.dcas.common.domain.entity.TemplateContent;

import java.util.List;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2023/2/14 11:03
 * @since 1.2.0
 */
public class TemplateProduceContext {
    TemplateProducer producer;

    public TemplateProduceContext(TemplateAlgorithm algorithm) {
        switch (algorithm) {
            case INTERSECTION :
                producer = new IntersectionProducer();
                break;
            default :
                throw new UnsupportedOperationException("Unknown support enum: " + algorithm);
        }
    }

    public List<TemplateContent> produce(Template template, List<Question> questions, List<Item> items) {
        return producer.produce(template, questions, items);
    }
}
