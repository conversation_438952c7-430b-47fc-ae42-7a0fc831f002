package com.dcas.system.producer;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.dcas.common.domain.entity.Item;
import com.dcas.common.domain.entity.Question;
import com.dcas.common.domain.entity.Template;
import com.dcas.common.domain.entity.TemplateContent;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * <p>
 *     标签匹配 求交集
 * </p>
 *
 * <AUTHOR>
 * @date 2023/2/14 11:30
 * @since 1.2.0
 */
public class IntersectionProducer implements TemplateProducer {

    /**
     * 以上均不符合。
     */
    private static final String ITEM_ID = "00000000000000000000000000000000";

    @Override
    public List<TemplateContent> produce(Template template, List<Question> questions, List<Item> items) {
        final List<TemplateContent> res = new ArrayList<>();
        questions.forEach(q -> {
            items.forEach(i -> {
                // 比较对象id
                if (CollUtil.isEmpty(intersectionDistinct(i.getObjectIds(), q.getObjectIds())))
                    return;
                Set<String> matchTagIds = intersectionDistinct(q.getTagIds(), i.getTagIds());
                if (Objects.equals(i.getId(), ITEM_ID) || CollUtil.isNotEmpty(matchTagIds)) {
                    TemplateContent content = TemplateContent.builder()
                            .templateId(template.getId())
                            .questionId(q.getId())
                            .itemId(i.getId())
                            .objectIds(q.getObjectIds())
                            .matchTags(CollUtil.isEmpty(matchTagIds) ? "0" : String.join(StrUtil.COMMA, matchTagIds))
                            .build();
                    res.add(content);
                }
            });
        });
        return res;
    }

    private Set<String> intersectionDistinct(String s1, String s2) {
        List<String> qTags = StrUtil.split(s1, StrUtil.COMMA);
        List<String> iTags = StrUtil.split(s2, StrUtil.COMMA);
        return CollUtil.intersectionDistinct(qTags, iTags);
    }
}
