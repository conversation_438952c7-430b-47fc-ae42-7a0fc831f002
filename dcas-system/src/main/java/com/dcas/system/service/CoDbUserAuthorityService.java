package com.dcas.system.service;

import com.dcas.common.domain.entity.CoDbUserAuthority;

import java.util.List;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2022/12/27 11:17
 * @since 1.0.1
 */
public interface CoDbUserAuthorityService {

    /**
     * 批量插入
     *
     * @param list CoDbUserAuthority
     */
    void save(List<CoDbUserAuthority> list);

    /**
     * 根据jobId删除记录
     *
     * @param jobId 权限查询作业id
     */
    void deleteByJobId(Long jobId);
}
