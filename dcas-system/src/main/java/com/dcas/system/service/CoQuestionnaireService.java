package com.dcas.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dcas.common.core.domain.RequestModel;
import com.dcas.common.model.dto.CommonDto;
import com.dcas.common.model.dto.SaveQuestionnaireDTO;
import com.dcas.common.domain.entity.CoQuestionnaire;
import com.dcas.common.model.vo.QueryQuestionnaireVo;

/**
 * fetch data
 *
 * <AUTHOR>
 * @Date 2022/5/30 15:05
 * @ClassName ConOperationDetailService
 */
public interface CoQuestionnaireService extends IService<CoQuestionnaire> {
    /**
     * 保存作业
     *
     * @param dto request
     * @return * @return int
     * @Date 2022/5/31 10:33
     */
    int saveQuestionnaire(RequestModel<SaveQuestionnaireDTO> dto) throws Exception;


    /**
     * 查询问卷
     *
     * @param dto request
     * @return * @return List<QueryQuestionVo>
     * @Date 2022/5/31 18:30
     */
    QueryQuestionnaireVo queryQuestionnaire(RequestModel<CommonDto> dto);
}
