package com.dcas.system.service;

import com.dcas.common.domain.entity.SecurityQuestionnaire;
import com.mchz.dcas.client.model.dto.ItemDTO;

import java.util.List;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2023/7/3 16:24
 * @since 1.4.0
 */
public interface SecurityQuestionnaireService {

    void updateBatch(List<SecurityQuestionnaire> questionnaires);

    List<ItemDTO> selectByOperationId(Integer operationId, String categoryName, Boolean inapplicable);
}
