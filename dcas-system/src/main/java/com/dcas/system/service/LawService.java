package com.dcas.system.service;

import com.dcas.common.utils.PageResult;
import com.dcas.common.domain.entity.Law;
import com.dcas.common.domain.entity.LawItem;
import com.dcas.common.model.req.*;
import com.dcas.common.model.vo.ArticleItemVO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2023/3/3 16:53
 * @since 1.2.0
 */
public interface LawService {

    List<Law> listAll();

    PageResult<Law> pageQuery(Integer currentPage, Integer pageSize);

    void addLaw(LawFileReq req);

    void updateLaw(LawFileReq req);

    void deleteLaw(IdsReq req);

    void enable(Integer id);

    PageResult<LawItem> pageQueryLawItem(Integer id, Integer currentPage, Integer pageSize);

    void addLawItem(LawItemReq req);

    void updateLawItem(LawItemUpdateReq req);

    void deleteLawItems(ExcludeItemReq req);

    List<ArticleItemVO> qryArticleItems(String articleId);

    void addArticleItem(RelevanceInsertReq req);

    void deleteArticleItem(Integer id);

    List<Law> search(LawFileReq req);

    List<LawItem> searchItem(LawItemReq req);
}
