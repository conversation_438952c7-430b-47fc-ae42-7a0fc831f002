package com.dcas.system.service.impl;

import cn.hutool.core.util.StrUtil;
import com.dcas.common.domain.entity.LibrarySyncConfig;
import com.dcas.system.manager.DynamicSchemaManager;
import com.dcas.common.mapper.LibrarySyncConfigMapper;
import com.dcas.common.mapper.LibrarySyncHistoryMapper;
import com.dcas.system.service.ExecuteScriptService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.FileSystemResource;
import org.springframework.jdbc.datasource.init.DatabasePopulatorUtils;
import org.springframework.jdbc.datasource.init.ResourceDatabasePopulator;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.sql.DataSource;
import java.util.Date;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2023/5/12 13:44
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ExecuteScriptServiceImpl implements ExecuteScriptService {

    private final DataSource dataSource;
    private final LibrarySyncConfigMapper librarySyncConfigMapper;
    private final LibrarySyncHistoryMapper librarySyncHistoryMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void executeSqlScript(String sqlPath) {
        log.info("开始执行知识库备份文件，path:{}", sqlPath);
        long start = System.currentTimeMillis();
        ResourceDatabasePopulator pop = new ResourceDatabasePopulator();
        FileSystemResource sqlResource = new FileSystemResource(sqlPath);
        pop.addScript(sqlResource);
        DatabasePopulatorUtils.execute(pop, dataSource);
        String fileName = StrUtil.subAfter(sqlPath, StrUtil.SLASH, true);
        String version = StrUtil.subBetween(fileName, StrUtil.DASHED, StrUtil.DOT);
        librarySyncHistoryMapper.updateSyncByVersion(version, sqlPath, new Date());
        librarySyncConfigMapper.updateStatus();
        librarySyncConfigMapper.insert(LibrarySyncConfig.builder().version(version).schemaName(version).isUsed(Boolean.TRUE).isLatest(Boolean.TRUE).build());
        DynamicSchemaManager.addSchema(version, version);
        log.info("知识库备份文件执行完成，耗时：{}ms", System.currentTimeMillis() - start);
    }
}
