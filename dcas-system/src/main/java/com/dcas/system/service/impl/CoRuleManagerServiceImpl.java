package com.dcas.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dcas.common.enums.NumEnum;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.dcas.common.core.domain.RequestModel;
import com.dcas.common.exception.params.FailParamsException;
import com.dcas.common.utils.DateUtils;
import com.dcas.common.utils.params.CheckUtil;
import com.dcas.common.model.dto.AddModelDto;
import com.dcas.common.model.dto.PrimaryKeyListDTO;
import com.dcas.common.model.dto.QueryModelDTO;
import com.dcas.common.model.dto.UpdateModelDTO;
import com.dcas.common.domain.entity.CoModel;
import com.dcas.common.mapper.CoModelMapper;
import com.dcas.system.service.RuleManagerService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * fetch data
 *
 * <AUTHOR>
 * @Date 2022/6/15 18:18
 * @ClassName RuleManagerServiceImpl
 */
@Service
public class CoRuleManagerServiceImpl implements RuleManagerService {

    @Autowired
    private CoModelMapper coModelMapper;

    /**
     * 新增合规模板
     *
     * @param dto request
     * @return * @return RestResponse
     * <AUTHOR>
     * @Date 2022/5/16 14:51
     */
    @Transactional(rollbackFor = Throwable.class)
    @Override
    public int add(RequestModel<AddModelDto> dto) throws ParseException {
        //入参校验
        CheckUtil.checkParams(dto.getPrivator());
        //返回值
        int row = 0;

        CoModel entity = new CoModel();
        //AddModelDto对象复制到entity
        BeanUtils.copyProperties(dto.getPrivator(), entity);

        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date parse = formatter.parse(DateUtils.dateTimeNow());
        entity.setCreateTime(parse);

        //保存
        row = coModelMapper.insert(entity);
        return row;
    }

    /**
     * 删除合规模板
     *
     * @param dto request
     * @return * @return RestResponse
     * <AUTHOR>
     * @Date 2022/5/16 15:39
     */
    @Transactional(rollbackFor = Throwable.class)
    @Override
    public int delete(RequestModel<PrimaryKeyListDTO> dto) {
        //入参校验
        CheckUtil.checkParams(dto.getPrivator());
        //返回值
        int row = 0;

        //删除前先查询,不存在数据抛异常
        QueryWrapper<CoModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("model_id", dto.getPrivator().getIdList());
        List<CoModel> list = coModelMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(list)) {
            throw new FailParamsException("数据不存在");
        }
        row = coModelMapper.deleteBatchIds(list);
        return row;
    }

    /**
     * 更新合规模板
     *
     * @param dto request
     * @return * @return RestResponse
     * <AUTHOR>
     * @Date 2022/5/16 15:46
     */
    @Transactional(rollbackFor = Throwable.class)
    @Override
    public int update(RequestModel<UpdateModelDTO> dto) {
        //入参校验
        CheckUtil.checkParams(dto.getPrivator());
        //返回值
        int row = 0;
        //更新前根据主键查询
        CoModel entity = coModelMapper.selectById(dto.getPrivator().getModelId());
        if (ObjectUtils.isEmpty(entity)) {
            throw new FailParamsException("数据不存在");
        }
        //复制对象
        CoModel coModel = new CoModel();
        BeanUtils.copyProperties(dto.getPrivator(), coModel);
        row = coModelMapper.updateById(coModel);
        return row;
    }

}
