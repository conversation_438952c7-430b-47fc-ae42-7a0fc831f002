package com.dcas.system.service;

import com.dcas.common.core.domain.RequestModel;
import com.dcas.common.utils.PageResult;
import com.dcas.common.model.dto.DetectionResultUpdateDTO;
import com.dcas.common.model.dto.ExportWordDto;
import com.dcas.common.domain.entity.DetectionResult;
import com.dcas.common.model.other.OptionSelect;
import com.dcas.common.model.param.DetectionQueryParam;
import com.dcas.common.model.vo.DetectionReportVO;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2023/10/12 15:58
 * @since 1.5.0
 */
public interface DetectionResultService {

    PageResult<DetectionResult> pageQuery(DetectionQueryParam param);

    void update(DetectionResultUpdateDTO dto);

    Boolean canFinish(String operationId);

    List<OptionSelect<Integer>> queryDbTemplate();

    void templateDownload(Integer dbType, HttpServletResponse response);

    void templateImport(MultipartFile file, String operationId);

    /**
     * 获取默认检测项建议措施
     * @param ruleId
     * @return
     */
    String getDefaultSuggest(Integer ruleId);

    /**
     * 基础评估 - 分析结果页面查看  汇总
     * @param operationId 作业ID
     * @return
     */
    DetectionReportVO summary(String operationId);

    void clear(String operationId);

    void export(HttpServletResponse response, String operationId) throws IOException;

    void exportWordReport(HttpServletResponse response,  RequestModel<ExportWordDto> dto)throws IOException;

    void downloadOfflineTool(HttpServletResponse response);
}
