package com.dcas.system.service;

import com.dcas.common.model.dto.CommonDto;
import com.dcas.common.model.vo.OnlineSecurityContentVO;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2025/5/15 10:28
 * @since 1.0.0
 */
public interface SecurityOfflineService {
    void offlineQuestionnaireExport(String operationId, HttpServletResponse response);

    void offlineQuestionnaireImport(String operationId, MultipartFile file);

    OnlineSecurityContentVO selectOnlineQuestionnaire(String operationId, Integer jobType);

    void saveOnlineQuestionnaire(OnlineSecurityContentVO vo);

    void onlineQuestionnaireImport(String operationId);
}
