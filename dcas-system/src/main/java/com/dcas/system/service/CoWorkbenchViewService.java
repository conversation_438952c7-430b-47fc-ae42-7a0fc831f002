package com.dcas.system.service;

import com.dcas.common.core.domain.BaseEntity;
import com.dcas.common.core.domain.RequestModel;
import com.dcas.common.model.dto.QueryOperationOnGoingViewDto;
import com.dcas.common.model.vo.QueryOperationOnGoingView;
import com.dcas.common.model.vo.QueryOperationStatusVo;

import java.text.ParseException;
import java.util.List;
import java.util.Map;

/**
 * 工作台视图服务层
 */
public interface CoWorkbenchViewService {

    /**
     * 查询作业状态
     */
    QueryOperationStatusVo retrieveStatus(QueryOperationOnGoingViewDto dto);

    /**
     * 查询执行中作业列表
     */
    List<QueryOperationOnGoingView> selectOperation(RequestModel<QueryOperationOnGoingViewDto> dto);

    /**
     * 查询待复核作业列表
     */
    List<QueryOperationOnGoingView> retrieveWaitReview(RequestModel<QueryOperationOnGoingViewDto> dto);

    /**
     * 作业分布图表
     */
    Map<String, Map<String, Double>> retrieveRelated(BaseEntity entity);
}
