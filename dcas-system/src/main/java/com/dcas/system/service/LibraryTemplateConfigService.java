package com.dcas.system.service;

import com.dcas.common.enums.TemplateTypeEnum;
import com.dcas.common.model.vo.LibraryTemplateVO;
import com.dcas.common.utils.PageResult;

/**
 * <AUTHOR>
 * @className LibraryTemplateConfigService
 * @description 知识库模板关联配置service
 * @date 2024/05/15 15:56
 */
public interface LibraryTemplateConfigService {

    void updateStatus(Integer relId, boolean enable, int type);

    /**
     * 分页查询模板信息
     *
     * @param currentPage
     *     当前页
     * @param pageSize
     *     每页大小
     * @param type
     *     模板类型{@link TemplateTypeEnum}
     * @return PageResult<AnalysisTemplate>
     */
    PageResult<LibraryTemplateVO> list(Integer currentPage, Integer pageSize, TemplateTypeEnum type);

    /**
     * 启用禁用
     *
     * @param id
     *     模板ID
     * @param type
     *     模板类型{@link TemplateTypeEnum}
     */
    void enable(Integer id, TemplateTypeEnum type);


    /**
     * 同步
     */
    void sync();
}
