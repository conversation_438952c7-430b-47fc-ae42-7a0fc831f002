package com.dcas.system.service;

import com.dcas.common.domain.entity.TaskExecution;

import java.util.List;

/**
 * 任务执行记录Service接口
 * 
 * <AUTHOR>
 */
public interface ITaskExecutionService {
    
    /**
     * 查询任务执行记录
     *
     * @param executionId 任务执行记录主键
     * @return 任务执行记录
     */
    TaskExecution selectTaskExecutionByExecutionId(Long executionId);

    /**
     * 查询任务执行记录列表
     *
     * @param taskExecution 任务执行记录
     * @return 任务执行记录集合
     */
    List<TaskExecution> selectTaskExecutionList(TaskExecution taskExecution);

    /**
     * 根据任务ID查询执行记录列表
     *
     * @param taskId 任务ID
     * @return 执行记录列表
     */
    List<TaskExecution> selectTaskExecutionByTaskId(Long taskId);

    /**
     * 新增任务执行记录
     *
     * @param taskExecution 任务执行记录
     * @return 结果
     */
    int insertTaskExecution(TaskExecution taskExecution);

    /**
     * 修改任务执行记录
     *
     * @param taskExecution 任务执行记录
     * @return 结果
     */
    int updateTaskExecution(TaskExecution taskExecution);

    /**
     * 批量删除任务执行记录
     *
     * @param executionIds 需要删除的任务执行记录主键集合
     * @return 结果
     */
    int deleteTaskExecutionByExecutionIds(Long[] executionIds);

    /**
     * 删除任务执行记录信息
     *
     * @param executionId 任务执行记录主键
     * @return 结果
     */
    int deleteTaskExecutionByExecutionId(Long executionId);

    /**
     * 根据任务ID删除所有执行记录
     *
     * @param taskId 任务ID
     * @return 结果
     */
    int deleteTaskExecutionByTaskId(Long taskId);

    /**
     * 创建新的执行记录
     *
     * @param taskId 任务ID
     * @param executorId 执行者ID
     * @param executorName 执行者名称
     * @return 执行记录
     */
    TaskExecution createTaskExecution(Long taskId, Long executorId, String executorName);

    /**
     * 开始执行记录
     *
     * @param executionId 执行记录ID
     * @param totalSteps 总步骤数
     * @return 结果
     */
    boolean startExecution(Long executionId, Integer totalSteps);

    /**
     * 完成执行记录
     *
     * @param executionId 执行记录ID
     * @param executionResult 执行结果
     * @return 结果
     */
    boolean completeExecution(Long executionId, String executionResult);

    /**
     * 标记执行失败
     *
     * @param executionId 执行记录ID
     * @param errorMessage 错误信息
     * @return 结果
     */
    boolean failExecution(Long executionId, String errorMessage);

    /**
     * 终止执行记录
     *
     * @param executionId 执行记录ID
     * @param terminationReason 终止原因
     * @return 结果
     */
    boolean terminateExecution(Long executionId, String terminationReason);

    /**
     * 更新执行进度
     *
     * @param executionId 执行记录ID
     * @param currentStep 当前步骤
     * @param progressPercentage 进度百分比
     * @param successSteps 成功步骤数
     * @param failedSteps 失败步骤数
     * @param skippedSteps 跳过步骤数
     * @return 结果
     */
    boolean updateExecutionProgress(Long executionId, Integer currentStep, Integer progressPercentage,
                                          Integer successSteps, Integer failedSteps, Integer skippedSteps);

    /**
     * 添加执行日志
     *
     * @param executionId 执行记录ID
     * @param logMessage 日志信息
     * @return 结果
     */
    boolean appendExecutionLog(Long executionId, String logMessage);

    /**
     * 根据执行批次号查询执行记录
     *
     * @param executionBatch 执行批次号
     * @return 执行记录
     */
    TaskExecution getTaskExecutionByBatch(String executionBatch);

    /**
     * 查询任务的最新执行记录
     *
     * @param taskId 任务ID
     * @return 最新执行记录
     */
    TaskExecution getLatestTaskExecution(Long taskId);

    /**
     * 查询运行中的执行记录
     *
     * @return 运行中的执行记录列表
     */
    List<TaskExecution> getRunningExecutions();

    /**
     * 根据状态查询执行记录
     *
     * @param status 状态
     * @return 执行记录列表
     */
    List<TaskExecution> getExecutionsByStatus(String status);

    /**
     * 生成执行批次号
     *
     * @param taskId 任务ID
     * @return 执行批次号
     */
    String generateExecutionBatch(Long taskId);

    /**
     * 增加重试次数
     *
     * @param executionId 执行记录ID
     * @return 结果
     */
    boolean incrementRetryCount(Long executionId);

    /**
     * 检查执行记录是否可以终止
     *
     * @param executionId 执行记录ID
     * @return 是否可以终止
     */
    boolean canTerminateExecution(Long executionId);

    /**
     * 检查执行记录是否正在运行
     *
     * @param executionId 执行记录ID
     * @return 是否正在运行
     */
    boolean isExecutionRunning(Long executionId);

    /**
     * 计算执行时长
     *
     * @param executionId 执行记录ID
     * @return 执行时长（毫秒）
     */
    Long calculateExecutionDuration(Long executionId);
}
