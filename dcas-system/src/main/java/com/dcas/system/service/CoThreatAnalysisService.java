package com.dcas.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dcas.common.domain.entity.ThreatFrequency;
import com.dcas.common.model.dto.*;
import com.dcas.common.model.vo.CoThreatAnalysisVO;
import com.dcas.common.model.vo.SourceBusinessVO;
import com.dcas.common.model.vo.ThreatTreeVO;
import com.github.pagehelper.PageInfo;
import com.dcas.common.core.domain.RequestModel;
import com.dcas.common.domain.entity.CoThreatAnalysis;

import java.util.List;

/**
 * fetch data
 *
 * <AUTHOR>
 * @Date 2022/8/9 9:52
 * @ClassName ThreatAnalysisService
 */
public interface CoThreatAnalysisService extends IService<CoThreatAnalysis> {

    /**
     * 威胁分析--查询业务系统
     *
     */
    List<SourceBusinessVO.SourceBusiness> retrieveSystem(String operationId);


    /**
     * 威胁分析--添加
     *
     * @param dto request
     * @return * @return RestResponse
     * <AUTHOR>
     * @Date 2022/5/16 14:51
     */
    int add(AddThreatAnalysisDTO dto);

    /**
     * 威胁分析--修改
     *
     * @param dto request
     * @return * @return RestResponse
     * <AUTHOR>
     * @Date 2022/5/16 14:51
     */
    int edit(AddThreatAnalysisDTO dto);

    /**
     * 威胁分析--查询
     *
     * @param dto request
     * @return * @return List<CoThreatAnalysis>
     * @Date 2022/8/9 14:30
     */
    PageInfo<CoThreatAnalysisVO> retrieve(CommonDto dto, Integer pageNo, Integer pageSize);

    /**
     * 删除威胁分类
     * @param threatTreeId 威胁分类ID
     * @return
     */
    Boolean deleteThreatTree(String threatTreeId);

    List<ThreatTreeVO> autoCalcFrequency(ThreatFrequencyCalcDTO dto);

    List<ThreatFrequency> getThreatFrequency(String operationId);

    void addMidThreatResult(AddThreatAnalysisDTO dto);

    void editMidThreatResult(AddThreatAnalysisDTO dto);

    void batchUpdateFrequency(String operationId, String frequency);

    void deleteTreeChild(Long id);

    void batchUpdateSelectedFrequency(BatchUpdateFrequencyDTO dto);

    void checkBusSystem(BusSystemCheckDTO dto);
}