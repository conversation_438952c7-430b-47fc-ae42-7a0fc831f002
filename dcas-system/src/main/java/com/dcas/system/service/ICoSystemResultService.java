package com.dcas.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dcas.common.domain.entity.CoSystemResult;

import java.util.List;

/**
 * <p>
 * 业务系统结果表，包含合规符合性结果和能力符合性结果 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-25
 */
public interface ICoSystemResultService extends IService<CoSystemResult> {

    /**
     * 复制合规系统结果
     *
     * @param operationId
     *     旧作业ID
     * @param newId
     *     新作业ID
     * @param objectIds
     *      业务系统ID
     */
    void copySystemResult(String operationId, String newId, List<Long> objectIds);
}
