package com.dcas.system.service;


import com.dcas.common.domain.entity.ScanSourceConfig;
import com.dcas.common.model.dto.DatabaseNotificationDTO;
import com.dcas.common.model.dto.ScanSourceConfigQueryDTO;
import com.dcas.common.utils.PageResult;

/**
 * <p>
 * 扫描数据源 服务类
 * </p>
 *
 * <AUTHOR>
 * @date 2024/4/28 10:30
 * @since 1.7.2
 */
public interface ScanSourceConfigService {

    /**
     * 保存数据库扫描结果
     *
     * @param notification 数据库扫描通知信息
     */
    void saveScanResult(DatabaseNotificationDTO notification);

    /**
     * 分页查询扫描数据源配置
     *
     * @param query 查询条件
     * @return 分页结果
     */
    PageResult<ScanSourceConfig> pageQuery(ScanSourceConfigQueryDTO query);
}