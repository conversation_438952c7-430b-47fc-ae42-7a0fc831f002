package com.dcas.system.service;

import com.dcas.common.utils.PageResult;
import com.dcas.common.model.dto.DisposalSearchDTO;
import com.dcas.common.domain.entity.AdviseScheme;
import com.dcas.common.model.param.AdviseSchemaSelectParam;
import com.dcas.common.model.req.DisposalStrategyReq;
import com.dcas.common.model.req.IdsReq;
import com.dcas.common.model.vo.DisposalStrategyVO;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2023/4/18 16:18
 * @since 1.3.0
 */
public interface DisposalStrategyService {

    PageResult<DisposalStrategyVO> list(@RequestParam(value = "currentPage", defaultValue = "1") @ApiParam("当前页码") Integer currentPage,
                                        @RequestParam(value = "pageSize", defaultValue = "10") @ApiParam("每页数量") Integer pageSize);

    DisposalStrategyVO detail(Integer id);

    void add(DisposalStrategyReq req);

    void batchDelete(IdsReq req);

    void update(Integer id, DisposalStrategyReq req);

    List<DisposalStrategyVO> search(DisposalSearchDTO dto);

    List<AdviseScheme> selectSchemeByItemIdsForLegal(AdviseSchemaSelectParam param);

    List<AdviseScheme> selectSchemeByItemIdsForCapacity(AdviseSchemaSelectParam param);
}
