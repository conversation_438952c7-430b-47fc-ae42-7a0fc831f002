package com.dcas.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dcas.common.core.domain.RequestModel;
import com.dcas.common.core.domain.TreeSelect;
import com.dcas.common.domain.entity.CoImpactAnalysis;
import com.dcas.common.model.dto.AddImpactAnalysisDTO;
import com.dcas.common.model.dto.CommonDto;
import com.dcas.common.model.vo.CoImpactAnalysisVO;
import com.dcas.common.model.vo.SourceBusinessVO;
import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * <p>
 * 影响分析表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-18
 */
public interface ICoImpactAnalysisService extends IService<CoImpactAnalysis> {

    List<SourceBusinessVO.SourceBusiness> retrieveSystem(String operationId);

    List<TreeSelect> selectTreeList(String operationId);

    int add(List<AddImpactAnalysisDTO> dto);

    int edit(List<AddImpactAnalysisDTO> dto);

    PageInfo<CoImpactAnalysisVO> retrieve(CommonDto privator, Integer pageNum, Integer pageSize);

    Boolean deleteImpactTree(String impactTreeId);
}
